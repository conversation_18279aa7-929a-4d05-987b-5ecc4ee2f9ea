<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D FPS 枪战游戏</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: #000;
            font-family: Arial, sans-serif;
            cursor: none;
        }
        
        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #ui {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 100;
        }
        
        #crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid #fff;
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }
        
        #hud {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: #fff;
            font-size: 18px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }
        
        #health {
            color: #ff4444;
            margin-bottom: 10px;
        }
        
        #ammo {
            color: #44ff44;
            margin-bottom: 10px;
        }
        
        #score {
            color: #4444ff;
        }
        
        #instructions {
            position: absolute;
            top: 20px;
            right: 20px;
            color: #fff;
            font-size: 14px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            text-align: right;
        }
        
        #gameOver {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #fff;
            font-size: 48px;
            text-align: center;
            display: none;
            background: rgba(0, 0, 0, 0.8);
            padding: 40px;
            border-radius: 10px;
        }
        
        #startScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: #fff;
            z-index: 200;
        }
        
        #startButton {
            padding: 20px 40px;
            font-size: 24px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 20px;
            pointer-events: all;
        }
        
        #startButton:hover {
            background: #45a049;
        }
        
        .muzzle-flash {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, #ffff00 0%, #ff8800 50%, transparent 100%);
            border-radius: 50%;
            opacity: 0;
            pointer-events: none;
            z-index: 50;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <div id="startScreen">
            <h1>3D FPS 枪战游戏</h1>
            <p>WASD - 移动</p>
            <p>鼠标 - 瞄准</p>
            <p>左键 - 射击</p>
            <p>R - 重新装弹</p>
            <button id="startButton">开始游戏</button>
        </div>
        
        <div id="ui">
            <div id="crosshair"></div>
            <div class="muzzle-flash" id="muzzleFlash"></div>
            
            <div id="hud">
                <div id="health">生命值: <span id="healthValue">100</span></div>
                <div id="ammo">弹药: <span id="ammoValue">30</span> / <span id="totalAmmo">90</span></div>
                <div id="score">得分: <span id="scoreValue">0</span></div>
            </div>
            
            <div id="instructions">
                <div>WASD - 移动</div>
                <div>鼠标 - 瞄准</div>
                <div>左键 - 射击</div>
                <div>R - 重新装弹</div>
            </div>
            
            <div id="gameOver">
                <div>游戏结束!</div>
                <div style="font-size: 24px; margin-top: 20px;">最终得分: <span id="finalScore">0</span></div>
                <div style="font-size: 16px; margin-top: 20px;">按 F5 重新开始</div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="game.js"></script>
</body>
</html>
