# 3D FPS 枪战游戏

一个使用 Three.js 开发的基于浏览器的 3D 第一人称射击游戏。

## 游戏特性

### 核心功能
- **3D 环境**: 使用 Three.js 渲染的完整 3D 游戏世界
- **第一人称视角**: 真实的 FPS 游戏体验
- **武器系统**: 包含射击、装弹、弹药管理
- **敌人 AI**: 智能敌人会追踪并攻击玩家
- **碰撞检测**: 子弹与敌人、墙壁的碰撞检测
- **得分系统**: 击败敌人获得分数
- **生命值系统**: 玩家生命值管理

### 视觉效果
- **动态光照**: 环境光和方向光
- **阴影效果**: 实时阴影渲染
- **枪口闪光**: 射击时的视觉效果
- **受伤效果**: 受到攻击时的红色闪烁
- **准星**: 屏幕中央的瞄准准星

### 游戏机制
- **移动系统**: WASD 键控制移动
- **鼠标控制**: 鼠标控制视角和瞄准
- **射击系统**: 左键射击，有射击间隔限制
- **装弹系统**: R 键重新装弹，有装弹时间
- **敌人生成**: 动态敌人生成系统
- **地图边界**: 防止玩家离开游戏区域

## 操作说明

### 基本控制
- **W**: 向前移动
- **S**: 向后移动
- **A**: 向左移动
- **D**: 向右移动
- **鼠标移动**: 控制视角
- **左键**: 射击
- **R**: 重新装弹

### 游戏目标
- 击败尽可能多的敌人
- 避免被敌人攻击
- 管理好弹药和生命值
- 获得更高的分数

## 技术实现

### 使用的技术
- **Three.js**: 3D 图形渲染引擎
- **HTML5**: 游戏界面和结构
- **CSS3**: 样式和UI设计
- **JavaScript**: 游戏逻辑和交互
- **Pointer Lock API**: 鼠标控制

### 文件结构
```
├── index.html          # 主HTML文件
├── game.js            # 游戏逻辑
├── server.js          # Node.js 服务器
├── server.py          # Python 服务器（备选）
└── README.md          # 说明文档
```

## 运行游戏

### 方法1: 使用 Node.js
```bash
node server.js
```

### 方法2: 使用 Python
```bash
python3 server.py
```

### 方法3: 直接打开
如果你有本地服务器，也可以直接在浏览器中打开 `index.html`

## 游戏截图和功能

### 主要界面元素
- **开始屏幕**: 游戏说明和开始按钮
- **HUD界面**: 显示生命值、弹药、得分
- **准星**: 屏幕中央的瞄准辅助
- **游戏结束屏幕**: 显示最终得分

### 游戏世界
- **地面**: 绿色半透明地面
- **围墙**: 棕色围墙形成游戏边界
- **障碍物**: 地图中的各种障碍物
- **敌人**: 红色方块敌人，会主动攻击玩家

## 开发说明

### 扩展功能建议
- 添加更多武器类型
- 实现不同类型的敌人
- 添加音效和背景音乐
- 实现多人游戏模式
- 添加更复杂的地图
- 实现道具和升级系统

### 性能优化
- 对象池管理子弹和敌人
- LOD (Level of Detail) 系统
- 视锥剔除优化
- 纹理压缩和优化

## 浏览器兼容性

支持现代浏览器：
- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

需要支持：
- WebGL
- Pointer Lock API
- ES6+ JavaScript

## 许可证

此项目仅供学习和演示使用。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进游戏！
