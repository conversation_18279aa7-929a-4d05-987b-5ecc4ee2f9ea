// 游戏状态
let gameState = {
    isPlaying: false,
    health: 100,
    ammo: 30,
    totalAmmo: 90,
    score: 0,
    enemies: [],
    bullets: [],
    lastShot: 0,
    fireRate: 200, // 毫秒
    isReloading: false
};

// Three.js 核心对象
let scene, camera, renderer, controls;
let clock = new THREE.Clock();

// 玩家控制
let moveForward = false, moveBackward = false, moveLeft = false, moveRight = false;
let velocity = new THREE.Vector3();
let direction = new THREE.Vector3();

// 鼠标控制
let mouseX = 0, mouseY = 0;
let isMouseLocked = false;

// 游戏对象
let ground, walls = [];
let weapon;

// 初始化游戏
function init() {
    // 创建场景
    scene = new THREE.Scene();
    scene.fog = new THREE.Fog(0x404040, 50, 200);

    // 创建相机
    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.set(0, 10, 0);

    // 创建渲染器
    renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x87CEEB);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    document.getElementById('gameContainer').appendChild(renderer.domElement);

    // 创建光源
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(50, 100, 50);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    scene.add(directionalLight);

    // 创建地面
    createGround();
    
    // 创建墙壁
    createWalls();
    
    // 创建武器
    createWeapon();

    // 设置事件监听器
    setupEventListeners();

    // 开始渲染循环
    animate();
}

function createGround() {
    const groundGeometry = new THREE.PlaneGeometry(200, 200);
    const groundMaterial = new THREE.MeshLambertMaterial({ 
        color: 0x90EE90,
        transparent: true,
        opacity: 0.8
    });
    ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2;
    ground.receiveShadow = true;
    scene.add(ground);
}

function createWalls() {
    const wallMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
    
    // 创建围墙
    const wallPositions = [
        { x: 0, z: 100, width: 200, height: 20 },
        { x: 0, z: -100, width: 200, height: 20 },
        { x: 100, z: 0, width: 20, height: 200 },
        { x: -100, z: 0, width: 20, height: 200 }
    ];

    wallPositions.forEach(pos => {
        const wallGeometry = new THREE.BoxGeometry(pos.width, 20, pos.width === 20 ? pos.height : 20);
        const wall = new THREE.Mesh(wallGeometry, wallMaterial);
        wall.position.set(pos.x, 10, pos.z);
        wall.castShadow = true;
        wall.receiveShadow = true;
        walls.push(wall);
        scene.add(wall);
    });

    // 创建一些内部障碍物
    const obstacles = [
        { x: 30, z: 30, width: 10, height: 15 },
        { x: -30, z: -30, width: 10, height: 15 },
        { x: 50, z: -20, width: 15, height: 10 },
        { x: -40, z: 40, width: 12, height: 12 }
    ];

    obstacles.forEach(obs => {
        const obsGeometry = new THREE.BoxGeometry(obs.width, obs.height, obs.width);
        const obstacle = new THREE.Mesh(obsGeometry, wallMaterial);
        obstacle.position.set(obs.x, obs.height / 2, obs.z);
        obstacle.castShadow = true;
        obstacle.receiveShadow = true;
        walls.push(obstacle);
        scene.add(obstacle);
    });
}

function createWeapon() {
    // 创建简单的武器模型
    const weaponGroup = new THREE.Group();
    
    // 枪管
    const barrelGeometry = new THREE.CylinderGeometry(0.3, 0.3, 8);
    const barrelMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
    const barrel = new THREE.Mesh(barrelGeometry, barrelMaterial);
    barrel.rotation.z = Math.PI / 2;
    barrel.position.set(4, -2, 1);
    weaponGroup.add(barrel);
    
    // 枪托
    const stockGeometry = new THREE.BoxGeometry(2, 1, 0.5);
    const stockMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
    const stock = new THREE.Mesh(stockGeometry, stockMaterial);
    stock.position.set(-1, -2, 1);
    weaponGroup.add(stock);

    weapon = weaponGroup;
    camera.add(weapon);
    scene.add(camera);
}

function setupEventListeners() {
    // 键盘事件
    document.addEventListener('keydown', onKeyDown);
    document.addEventListener('keyup', onKeyUp);
    
    // 鼠标事件
    document.addEventListener('mousedown', onMouseDown);
    document.addEventListener('mousemove', onMouseMove);
    
    // 指针锁定事件
    document.addEventListener('pointerlockchange', onPointerLockChange);
    
    // 窗口大小调整
    window.addEventListener('resize', onWindowResize);
    
    // 开始按钮
    document.getElementById('startButton').addEventListener('click', startGame);
}

function startGame() {
    document.getElementById('startScreen').style.display = 'none';
    gameState.isPlaying = true;
    
    // 请求指针锁定
    renderer.domElement.requestPointerLock();
    
    // 生成敌人
    spawnEnemies();
}

function spawnEnemies() {
    // 清除现有敌人
    gameState.enemies.forEach(enemy => {
        scene.remove(enemy.mesh);
    });
    gameState.enemies = [];

    // 生成新敌人
    for (let i = 0; i < 5; i++) {
        createEnemy();
    }
}

function createEnemy() {
    const enemyGeometry = new THREE.BoxGeometry(2, 4, 2);
    const enemyMaterial = new THREE.MeshLambertMaterial({ color: 0xff0000 });
    const enemyMesh = new THREE.Mesh(enemyGeometry, enemyMaterial);
    
    // 随机位置
    let x, z;
    do {
        x = (Math.random() - 0.5) * 180;
        z = (Math.random() - 0.5) * 180;
    } while (Math.sqrt(x * x + z * z) < 20); // 确保不在玩家附近生成
    
    enemyMesh.position.set(x, 2, z);
    enemyMesh.castShadow = true;
    
    const enemy = {
        mesh: enemyMesh,
        health: 100,
        speed: 0.5,
        lastAttack: 0,
        attackRate: 2000
    };
    
    gameState.enemies.push(enemy);
    scene.add(enemyMesh);
}

// 事件处理函数
function onKeyDown(event) {
    switch (event.code) {
        case 'KeyW': moveForward = true; break;
        case 'KeyS': moveBackward = true; break;
        case 'KeyA': moveLeft = true; break;
        case 'KeyD': moveRight = true; break;
        case 'KeyR': reload(); break;
    }
}

function onKeyUp(event) {
    switch (event.code) {
        case 'KeyW': moveForward = false; break;
        case 'KeyS': moveBackward = false; break;
        case 'KeyA': moveLeft = false; break;
        case 'KeyD': moveRight = false; break;
    }
}

function onMouseDown(event) {
    if (event.button === 0 && gameState.isPlaying) { // 左键
        shoot();
    }
}

function onMouseMove(event) {
    if (!isMouseLocked) return;

    mouseX += event.movementX * 0.002;
    mouseY += event.movementY * 0.002;
    mouseY = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, mouseY));

    camera.rotation.y = -mouseX;
    camera.rotation.x = -mouseY;
}

function onPointerLockChange() {
    isMouseLocked = document.pointerLockElement === renderer.domElement;
}

function onWindowResize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
}

// 射击系统
function shoot() {
    const now = Date.now();
    if (now - gameState.lastShot < gameState.fireRate || gameState.ammo <= 0 || gameState.isReloading) {
        return;
    }

    gameState.lastShot = now;
    gameState.ammo--;
    updateUI();

    // 枪口闪光效果
    showMuzzleFlash();

    // 创建子弹
    createBullet();
}

function showMuzzleFlash() {
    const muzzleFlash = document.getElementById('muzzleFlash');
    muzzleFlash.style.opacity = '0.8';
    setTimeout(() => {
        muzzleFlash.style.opacity = '0';
    }, 50);
}

function createBullet() {
    const bulletGeometry = new THREE.SphereGeometry(0.1);
    const bulletMaterial = new THREE.MeshBasicMaterial({ color: 0xffff00 });
    const bulletMesh = new THREE.Mesh(bulletGeometry, bulletMaterial);

    // 设置子弹起始位置
    bulletMesh.position.copy(camera.position);

    // 计算射击方向
    const direction = new THREE.Vector3();
    camera.getWorldDirection(direction);

    const bullet = {
        mesh: bulletMesh,
        direction: direction.clone(),
        speed: 2,
        life: 100
    };

    gameState.bullets.push(bullet);
    scene.add(bulletMesh);
}

function reload() {
    if (gameState.isReloading || gameState.totalAmmo <= 0) return;

    gameState.isReloading = true;

    setTimeout(() => {
        const needed = 30 - gameState.ammo;
        const available = Math.min(needed, gameState.totalAmmo);

        gameState.ammo += available;
        gameState.totalAmmo -= available;
        gameState.isReloading = false;

        updateUI();
    }, 2000);
}

// 更新游戏逻辑
function updateGame() {
    if (!gameState.isPlaying) return;

    const delta = clock.getDelta();

    // 更新玩家移动
    updatePlayerMovement(delta);

    // 更新子弹
    updateBullets();

    // 更新敌人
    updateEnemies(delta);

    // 检查游戏结束条件
    checkGameOver();
}

function updatePlayerMovement(delta) {
    velocity.x -= velocity.x * 10.0 * delta;
    velocity.z -= velocity.z * 10.0 * delta;

    direction.z = Number(moveForward) - Number(moveBackward);
    direction.x = Number(moveRight) - Number(moveLeft);
    direction.normalize();

    if (moveForward || moveBackward) velocity.z -= direction.z * 400.0 * delta;
    if (moveLeft || moveRight) velocity.x -= direction.x * 400.0 * delta;

    // 应用移动
    const moveVector = new THREE.Vector3();
    moveVector.setFromMatrixColumn(camera.matrix, 0);
    moveVector.crossVectors(camera.up, moveVector);
    moveVector.multiplyScalar(velocity.z * delta);

    const strafeVector = new THREE.Vector3();
    strafeVector.setFromMatrixColumn(camera.matrix, 0);
    strafeVector.multiplyScalar(velocity.x * delta);

    camera.position.add(moveVector);
    camera.position.add(strafeVector);

    // 限制玩家在地图范围内
    camera.position.x = Math.max(-95, Math.min(95, camera.position.x));
    camera.position.z = Math.max(-95, Math.min(95, camera.position.z));
    camera.position.y = 10; // 固定高度
}

function updateBullets() {
    for (let i = gameState.bullets.length - 1; i >= 0; i--) {
        const bullet = gameState.bullets[i];

        // 移动子弹
        bullet.mesh.position.add(bullet.direction.clone().multiplyScalar(bullet.speed));
        bullet.life--;

        // 检查子弹碰撞
        let hit = false;

        // 检查与敌人的碰撞
        for (let j = gameState.enemies.length - 1; j >= 0; j--) {
            const enemy = gameState.enemies[j];
            const distance = bullet.mesh.position.distanceTo(enemy.mesh.position);

            if (distance < 2) {
                // 击中敌人
                enemy.health -= 50;
                hit = true;

                if (enemy.health <= 0) {
                    // 敌人死亡
                    scene.remove(enemy.mesh);
                    gameState.enemies.splice(j, 1);
                    gameState.score += 100;
                    updateUI();

                    // 生成新敌人
                    if (gameState.enemies.length < 3) {
                        createEnemy();
                    }
                }
                break;
            }
        }

        // 检查与墙壁的碰撞
        if (!hit) {
            for (const wall of walls) {
                const box = new THREE.Box3().setFromObject(wall);
                if (box.containsPoint(bullet.mesh.position)) {
                    hit = true;
                    break;
                }
            }
        }

        // 移除子弹
        if (hit || bullet.life <= 0 || Math.abs(bullet.mesh.position.x) > 100 || Math.abs(bullet.mesh.position.z) > 100) {
            scene.remove(bullet.mesh);
            gameState.bullets.splice(i, 1);
        }
    }
}

function updateEnemies(delta) {
    gameState.enemies.forEach(enemy => {
        // 简单的AI：朝玩家移动
        const direction = new THREE.Vector3();
        direction.subVectors(camera.position, enemy.mesh.position);
        direction.y = 0;
        direction.normalize();

        // 移动敌人
        enemy.mesh.position.add(direction.multiplyScalar(enemy.speed * delta * 60));

        // 检查是否攻击玩家
        const distance = enemy.mesh.position.distanceTo(camera.position);
        const now = Date.now();

        if (distance < 5 && now - enemy.lastAttack > enemy.attackRate) {
            enemy.lastAttack = now;
            // 对玩家造成伤害
            gameState.health -= 20;
            updateUI();

            // 添加受伤效果
            document.body.style.background = 'rgba(255, 0, 0, 0.3)';
            setTimeout(() => {
                document.body.style.background = '#000';
            }, 200);
        }
    });
}

function checkGameOver() {
    if (gameState.health <= 0) {
        gameState.isPlaying = false;
        document.getElementById('finalScore').textContent = gameState.score;
        document.getElementById('gameOver').style.display = 'block';
        document.exitPointerLock();
    }
}

function updateUI() {
    document.getElementById('healthValue').textContent = gameState.health;
    document.getElementById('ammoValue').textContent = gameState.ammo;
    document.getElementById('totalAmmo').textContent = gameState.totalAmmo;
    document.getElementById('scoreValue').textContent = gameState.score;
}

// 渲染循环
function animate() {
    requestAnimationFrame(animate);

    updateGame();
    renderer.render(scene, camera);
}

// 启动游戏
init();
